# Task ID: 32
# Title: Backend Analytics Endpoints and Frontend Display
# Status: pending
# Dependencies: 31
# Priority: medium
# Description: Create backend analytics endpoints (`GET /api/analytics/probability`, `GET /api/analytics/mental-math`) and a 'Progress' page/section in the frontend to display this data.
# Details:
Backend (FastAPI):
- `GET /api/analytics/probability`: Query `user_attempts` table. Calculate and return statistics (e.g., overall accuracy, accuracy by topic, number of attempts, average time per question). Requires `topic` to be consistently stored or derived.
- `GET /api/analytics/mental-math`: Query `mental_math_sessions` table. Return data for trends (e.g., scores over time, average duration, accuracy if individual problem results were stored - for <PERSON>, session scores are primary).
Frontend (HTML/TypeScript):
- Create a new 'Progress' or 'Analytics' section/page in `index.html` or a separate HTML file.
- Add TypeScript logic to fetch data from the two new analytics endpoints.
- Display the fetched data using simple tables or textual statistics. For MVP, complex charts are not required.

# Test Strategy:
Populate the database with sample attempt and session data. Test both analytics endpoints using `curl` or <PERSON><PERSON> to verify correct data aggregation and JSON response format. In the browser, navigate to the 'Progress' page and verify that analytics data is fetched and displayed correctly and clearly.
