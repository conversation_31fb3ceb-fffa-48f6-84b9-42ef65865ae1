# Task ID: 27
# Title: Frontend Scaffolding for Probability/Statistics (HTML/CSS)
# Status: pending
# Dependencies: 26
# Priority: medium
# Description: Create the basic frontend scaffolding (`index.html`, `style.css`) for the Probability/Statistics module. Focus on a clean, intuitive interface.
# Details:
Create `static/index.html` with:
- A div to display the question content.
- A textarea for user's answer/reasoning.
- A submit button.
- A div to display feedback from the LLM.
Create `static/style.css` with basic styling for readability, layout, and a focused appearance. Ensure elements are clearly identifiable (e.g., using IDs for JavaScript interaction).

# Test Strategy:
Open `static/index.html` (served via FastAPI from Task 26) in a web browser. Verify the layout, presence of all UI elements (question area, input textarea, submit button, feedback area), and basic styling.
