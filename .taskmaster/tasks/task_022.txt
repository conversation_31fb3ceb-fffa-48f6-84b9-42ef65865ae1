# Task ID: 22
# Title: Database Schema Design and Initialization (SQLite with SQLAlchemy)
# Status: in-progress
# Dependencies: 21
# Priority: high
# Description: Define and create the SQLite database schema for `questions` and `user_attempts` tables using SQLAlchemy ORM. The schema will focus on core question and attempt data, deferring user accounts and detailed session logging for future iterations.
# Details:
Define SQLAlchemy models for the following tables. The database will be a local SQLite file (e.g., `quant_prep.db`).

**`questions` table:**
- `id` (Integer, Primary Key, autoincrement)
- `module` (String, nullable=False, index=True) - e.g., 'probability_statistics', 'mental_math'
- `topic` (String, nullable=False, index=True)
- `difficulty` (String, nullable=False, index=True)
- `content` (Text, nullable=False) - Stores JSON string for problem details (e.g., question text, options, type)
- `solution` (Text, nullable=False) - Stores JSON string or text for the correct answer and explanation steps
- `created_at` (DateTime, default=datetime.utcnow)
- `updated_at` (DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
- Relationships: `attempts` (relationship to `UserAttempt` model, back_populates="question")

**`user_attempts` table:**
- `id` (Integer, Primary Key, autoincrement)
- `question_id` (Integer, Foreign Key to `questions.id`, nullable=False, index=True)
- `user_answer` (Text, nullable=True) - User's final answer, if distinct from reasoning
- `user_reasoning` (Text, nullable=True) - User's reasoning submitted to the LLM
- `llm_response` (Text, nullable=True) - Structured JSON response from LLM (e.g., {"analysis": "...", "guidance_question": "...", "status": "..."})
- `is_correct` (Boolean, nullable=False) - Derived from `llm_response` (e.g., `llm_response['status'] == 'correct'`)
- `timestamp` (DateTime, default=datetime.utcnow, index=True)
- `time_taken_ms` (Integer, nullable=True) - Time taken for the attempt in milliseconds
- `attempt_number` (Integer, default=1, nullable=False) - For multiple tries on the same question instance by the user
- Relationships: `question` (relationship to `Question` model, back_populates="attempts")

**Implementation Considerations:**
- Translate these conceptual models into actual SQLAlchemy model classes (e.g., in a file like `app/database_models.py`).
- Create a script (e.g., `init_db.py`) that uses SQLAlchemy's `Base.metadata.create_all(engine)` to create these tables.
- Ensure ForeignKey constraints and SQLAlchemy `relationship()` attributes are correctly defined for data integrity and query facilitation.
- Columns frequently used in `WHERE` clauses, `JOIN` conditions, or `ORDER BY` clauses (e.g., `question_id`, `topic`, `module`, `timestamp`) should be indexed (`index=True`) for performance.
- Use `default=datetime.utcnow` (from Python's `datetime` module) for `created_at` and `timestamp` fields. For `updated_at`, use `onupdate=datetime.utcnow`.

# Test Strategy:
Run the initialization script (e.g., `init_db.py`). Verify the `quant_prep.db` file is created. Inspect the database schema using a SQLite browser or CLI to confirm that the `questions` and `user_attempts` tables are correctly defined with all specified columns, types, constraints (PK, FK, nullable, default), indexes, and relationships as per the refined schema.

# Subtasks:
## 1. Configure SQLAlchemy Engine and Declarative Base [in-progress]
### Dependencies: None
### Description: Set up the SQLAlchemy engine to connect to the `quant_prep.db` SQLite database and declare the `Base` for ORM models.
### Details:
Import `create_engine` from `sqlalchemy` and `declarative_base` from `sqlalchemy.ext.declarative`. Initialize `engine = create_engine('sqlite:///quant_prep.db')` and `Base = declarative_base()`. This forms the foundation for model definitions and database interaction.

## 2. Define `Question` SQLAlchemy Model [pending]
### Dependencies: 22.1
### Description: Create the SQLAlchemy ORM model for the `questions` table, including all specified fields, types, constraints, and relationships.
### Details:
Define a class `Question(Base)` with columns:
- `id`: `Column(Integer, primary_key=True, autoincrement=True)`
- `module`: `Column(String, nullable=False, index=True)`
- `topic`: `Column(String, nullable=False, index=True)`
- `difficulty`: `Column(String, nullable=False, index=True)`
- `content`: `Column(Text, nullable=False)`
- `solution`: `Column(Text, nullable=False)`
- `created_at`: `Column(DateTime, default=datetime.utcnow)`
- `updated_at`: `Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)`
Define the `attempts` relationship: `attempts = relationship('UserAttempt', back_populates='question')`.
Import necessary types from `sqlalchemy` (e.g., `Column`, `Integer`, `String`, `Text`, `DateTime`) and `relationship` from `sqlalchemy.orm`. Import `datetime` from `datetime`.

## 3. Define `UserAttempt` SQLAlchemy Model [pending]
### Dependencies: 22.1, 22.2
### Description: Create the SQLAlchemy ORM model for the `user_attempts` table, including all specified fields, types, constraints, and relationships.
### Details:
Define a class `UserAttempt(Base)` with columns:
- `id`: `Column(Integer, primary_key=True, autoincrement=True)`
- `question_id`: `Column(Integer, ForeignKey('questions.id'), nullable=False, index=True)`
- `user_answer`: `Column(Text, nullable=True)`
- `user_reasoning`: `Column(Text, nullable=True)`
- `llm_response`: `Column(Text, nullable=True)`
- `is_correct`: `Column(Boolean, nullable=False)`
- `timestamp`: `Column(DateTime, default=datetime.utcnow, index=True)`
- `time_taken_ms`: `Column(Integer, nullable=True)`
- `attempt_number`: `Column(Integer, default=1, nullable=False)`
Define the `question` relationship: `question = relationship('Question', back_populates='attempts')`.
Import necessary types from `sqlalchemy` (e.g., `Column`, `Integer`, `Text`, `Boolean`, `DateTime`, `ForeignKey`) and `relationship` from `sqlalchemy.orm`. Import `datetime` from `datetime`.

## 4. Implement Database Initialization Script (`init_db.py`) [pending]
### Dependencies: 22.1, 22.2, 22.3
### Description: Develop a Python script (`init_db.py`) that uses the defined SQLAlchemy models (`Question`, `UserAttempt`) and engine to generate the database schema and create all tables in the `quant_prep.db` SQLite file.
### Details:
The script should import `engine`, `Base`, and the `Question` and `UserAttempt` models from their respective definitions (e.g., `app/database_models.py`). It will then call `Base.metadata.create_all(engine)` to create the tables. Include a confirmation message (e.g., print statement) upon successful execution.

## 5. Verify Database Schema Integrity and Structure [pending]
### Dependencies: 22.4
### Description: Inspect the created `quant_prep.db` file to ensure the `questions` and `user_attempts` tables exist and their schemas (columns, types, keys, relationships, indexes) match the refined specifications.
### Details:
Use a SQLite database browser tool (e.g., DB Browser for SQLite) or programmatically query the database (e.g., using `sqlite3` module or SQLAlchemy's inspection capabilities) to check: table names (`questions`, `user_attempts`), column names, data types, primary key constraints, autoincrement settings, foreign key constraints, nullability, default values, and indexes for all fields as specified in the refined model definitions.

