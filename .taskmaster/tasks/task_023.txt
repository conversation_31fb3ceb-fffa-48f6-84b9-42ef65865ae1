# Task ID: 23
# Title: Populate Initial `questions` Table
# Status: pending
# Dependencies: 22
# Priority: medium
# Description: Create and execute a Python script to populate the `questions` table with 10-15 initial probability and statistics questions.
# Details:
The script will use SQLAlchemy to add records to the `questions` table. Questions should cover topics like Expected Value, Bayesian Reasoning, Combinatorics, etc., inspired by resources like 'Fifty Challenging Problems in Probability.' `content` field should store the question text, and `solution` field the detailed answer/explanation. Example question content: `{"text": "What is the probability of rolling a 7 with two standard dice?", "type": "calculation"}`.

# Test Strategy:
Execute the script. Query the `questions` table to verify that 10-15 questions are inserted correctly with all fields populated. Check data integrity and format of `content` and `solution`.
