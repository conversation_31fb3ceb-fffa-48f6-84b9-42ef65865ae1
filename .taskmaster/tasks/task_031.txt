# Task ID: 31
# Title: Implement Data Persistence for User Attempts and Sessions
# Status: pending
# Dependencies: 26, 30
# Priority: medium
# Description: Implement data persistence for user attempts in the Probability/Statistics module and for Mental Math sessions. This involves updating backend logic and potentially creating a new table for mental math sessions.
# Details:
Probability/Statistics:
- Modify `POST /api/submit-answer` (Task 26): After LLM interaction, save attempt details to `user_attempts` table. `is_correct` can be derived from the LLM's response `status` field (e.g., if `status` is 'correct' or similar). `time_taken_ms` can be calculated on the client and sent, or estimated on the server.
Mental Math:
- Define `mental_math_sessions` SQLAlchemy model: `id` (PK), `score` (Integer), `duration_ms` (Integer), `timestamp` (DateTime), `settings` (Text - stores JSON string for difficulty/type).
- Create this table in the database (update `init_db.py` or use migrations if `alembic` is introduced).
- Create a new FastAPI endpoint `POST /api/mental-math/submit-session` to receive session data (score, duration, settings) from the frontend and save it to `mental_math_sessions` table.

# Test Strategy:
After completing a probability question, verify a new record is added to `user_attempts` with correct data. After completing a mental math session and submitting results, verify a new record is added to `mental_math_sessions`. Check data integrity in the database.
