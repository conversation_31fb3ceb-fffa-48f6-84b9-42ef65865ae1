# Task ID: 30
# Title: Frontend UI and Logic for Mental Math Module
# Status: pending
# Dependencies: 28, 29
# Priority: medium
# Description: Implement the frontend UI and logic for the Mental Math Trainer module. This includes a new UI section/page, countdown timer, problem display, input handling, client-side answer checking, and session score display.
# Details:
In `static/index.html` (or a new HTML file if preferred, e.g., `mental_math.html` served by a new route):
- Add UI elements for: drill type/difficulty selection, start button, timer display, problem display area, answer input field, immediate feedback area, session score display.
In `static/ts/main.ts` (or a new TS file, e.g., `mental_math.ts`):
- Logic to fetch drill from `GET /api/mental-math/drill`.
- Implement countdown timer using `setInterval`/`clearInterval`.
- Display problems sequentially. Accept user answers.
- Perform client-side answer checking for immediate feedback (correct/incorrect).
- Calculate and display score at the end of the timed session.

# Test Strategy:
Manually test the Mental Math module in the browser. Verify:
1. Drill selection and fetching problems.
2. Timer functionality (starts, counts down, stops).
3. Correct display of problems and input fields.
4. Immediate feedback on answers.
5. Accurate score calculation and display at session end. Check for UI responsiveness and absence of console errors.
