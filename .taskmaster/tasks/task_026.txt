# Task ID: 26
# Title: FastAPI Backend for Probability/Statistics Module
# Status: pending
# Dependencies: 22, 24
# Priority: high
# Description: Expand FastAPI backend: create endpoints for the Probability/Statistics module (`GET /api/question/random`, `POST /api/submit-answer`) and configure FastAPI to serve static frontend assets.
# Details:
In `app/main.py` (or similar):
- `GET /api/question/random`: Fetches a random question object from the `questions` table (using SQLAlchemy) and returns it as JSON.
- `POST /api/submit-answer`: Expects JSON payload like `{"question_id": int, "user_reasoning": str}`. Calls the LLM Socratic Tutor function (Task 24). Returns the LLM's JSON response.
- Configure `StaticFiles` to serve `index.html`, `style.css`, and compiled `main.js` from a `static/` directory.

# Test Strategy:
Start the FastAPI server (`uvicorn app.main:app --reload`).
- Test `GET /api/question/random` using a browser or `curl`; verify a question <PERSON><PERSON><PERSON> is returned.
- Test `POST /api/submit-answer` using <PERSON><PERSON> or `curl` with a sample payload; verify LLM JSON response.
- Verify static file serving by navigating to `http://localhost:8000/index.html` in a browser (once Task 27 is done).
