# Task ID: 29
# Title: Backend for Mental Math Module (Problem Generator & API Endpoint)
# Status: pending
# Dependencies: 21
# Priority: medium
# Description: Develop `math_generator.py` for dynamic mental math problem generation and a new FastAPI endpoint `GET /api/mental-math/drill`.
# Details:
Create `app/math_generator.py`.
- Implement functions to generate problems: arithmetic (multi-digit multiplication/division, fractions, percentages), sequences, estimation.
- Functions should accept difficulty parameters.
In FastAPI (`app/main.py`):
- Create `GET /api/mental-math/drill` endpoint.
- Endpoint accepts parameters like `type` (e.g., 'multiplication', 'sequence') and `difficulty` (e.g., 'easy', 'medium', 'hard').
- Calls appropriate functions from `math_generator.py` and returns a JSON array of problems (e.g., `[{"problem": "123 * 45", "answer": "5535"}, ...]`).

# Test Strategy:
Unit test functions in `math_generator.py` for correctness and variety of generated problems. Test `GET /api/mental-math/drill` endpoint using `curl` or <PERSON><PERSON> with different `type` and `difficulty` parameters. Verify the structure and content of the returned JSON.
