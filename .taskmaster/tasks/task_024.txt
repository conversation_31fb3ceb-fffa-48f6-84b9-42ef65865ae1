# Task ID: 24
# Title: Core LLM Socratic Tutor Function
# Status: pending
# Dependencies: 21
# Priority: high
# Description: Implement the core Python function for Socratic tutoring using an LLM (e.g., GPT-4 via OpenAI API). This includes prompt engineering and handling structured JSON responses from the LLM.
# Details:
Create a Python module (e.g., `llm_tutor.py`).
Function signature: `def get_socratic_feedback(problem_context: str, user_reasoning: str) -> dict:`.
Use the system prompt provided in the PRD. Manage LLM API keys using a `.env` file and `python-dotenv`.
Ensure robust parsing of the LLM's JSON response: `{"analysis": "...", "guidance_question": "...", "status": "..."}`.
Implement error handling for API calls (e.g., network issues, API errors) and unexpected response formats.

# Test Strategy:
Unit test the function with mock LLM API responses to verify JSON parsing and error handling. Conduct integration tests with actual LLM API calls using sample problems and user reasoning to evaluate the effectiveness of the prompt and the structure of the response.
