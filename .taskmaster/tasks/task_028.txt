# Task ID: 28
# Title: Frontend Logic for Probability/Statistics (TypeScript)
# Status: pending
# Dependencies: 27
# Priority: high
# Description: Implement frontend logic in `main.ts` (TypeScript) for the Probability/Statistics module. This includes fetching questions, submitting answers, and displaying LLM feedback. Setup TypeScript compilation.
# Details:
Create `static/ts/main.ts`.
- On page load: Use `fetch` to call `GET /api/question/random` and populate the question display area.
- Submit button event listener: Get user input from textarea. Use `fetch` to call `POST /api/submit-answer` with `question_id` and `user_reasoning`. Display `analysis` and `guidance_question` from the JSON response in the feedback area.
- Setup TypeScript: `npm install -D typescript` (if using npm). Create `tsconfig.json` (e.g., `{"compilerOptions": {"target": "es6", "outFile": "../main.js"}}`). Compile `main.ts` to `static/main.js` using `tsc static/ts/main.ts --watch` or an npm script.

# Test Strategy:
Ensure `main.ts` compiles to `main.js` without errors. Open `index.html` in the browser. Verify: 
1. A random question loads on page init.
2. Submitting an answer calls the backend and displays LLM feedback.
3. UI updates correctly. Check browser developer console for any JavaScript errors.
