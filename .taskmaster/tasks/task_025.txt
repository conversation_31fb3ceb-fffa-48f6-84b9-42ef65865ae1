# Task ID: 25
# Title: Simple CLI for Backend Testing (`main_cli.py`)
# Status: pending
# Dependencies: 23, 24
# Priority: medium
# Description: Develop a simple command-line interface (`main_cli.py`) to test the backend loop: fetch a question from DB, get user input, process with LLM tutor, display feedback.
# Details:
The CLI script will:
1. Connect to the SQLite database (using SQLAlchemy session).
2. Fetch a random question from the `questions` table.
3. Display the question to the user.
4. Prompt the user for their answer/reasoning.
5. Call the LLM Socratic Tutor function (from Task 24) with the question context and user input.
6. Print the LLM's `analysis` and `guidance_question`.

# Test Strategy:
Run `python main_cli.py`. Interact with the CLI by answering several questions. Verify that questions are fetched correctly, user input is processed, and LLM feedback is displayed as expected. Check for any errors during the interaction loop.
