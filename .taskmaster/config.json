{"models": {"main": {"provider": "google", "modelId": "gemini-2.5-pro-preview-05-06", "maxTokens": 1048000, "temperature": 0.2}, "research": {"provider": "openrouter", "modelId": "perplexity/sonar", "maxTokens": 163840, "temperature": 0.1}, "fallback": {"provider": "openrouter", "modelId": "deepseek/deepseek-chat-v3-0324:free", "maxTokens": 163840, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "defaultTag": "master", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "userId": "**********"}}