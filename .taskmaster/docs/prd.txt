<context>
# Overview
This project is a locally-run desktop application designed to provide a focused, adaptive, and intelligent learning environment for preparing for quantitative finance interviews. Its primary purpose is to enhance skills in Probability & Statistics and Mental Math, and to serve as a practical project for strengthening proficiency in Python and TypeScript. The core innovation is its LLM integration for Socratic tutoring.

# Core Features
1.  **Probability & Statistics Deep-Dive Engine:**
    *   **What it does:** Provides a curated database of interview-style questions on topics like Expected Value, Bayesian Reasoning, Combinatorics, Conditional Probability, Distributions, and brain-teaser probability puzzles.
    *   **Why it's important:** Targets user-identified areas of weakness for mastery-level preparation.
    *   **How it works:** Uses an LLM-powered Socratic Tutor to provide hints, step-by-step solutions, and analyze user reasoning. Questions are inspired by resources like "Fifty Challenging Problems in Probability," QuantGuide, and TraderMath.

2.  **High-Speed Mental Math Trainer:**
    *   **What it does:** Offers dynamically generated drills for arithmetic (multi-digit multiplication/division, fractions, percentages), sequences, and estimation.
    *   **Why it's important:** Builds speed and accuracy for screening tests at firms like Optiver.
    *   **How it works:** Features timed sessions, customizable difficulty, and performance analytics to track speed and accuracy.

# User Experience
- **User Personas:** Aspiring quantitative finance professionals preparing for interviews, focusing on self-study and skill improvement in specific weak areas (Probability/Statistics, Mental Math).
- **Key User Flows:**
    1.  **Probability/Statistics Module:** User selects a topic -> Receives a question -> Submits answer/reasoning -> Interacts with Socratic LLM for hints, solution, or reasoning analysis.
    2.  **Mental Math Module:** User selects drill type and difficulty -> Starts timed session -> Answers dynamically generated problems -> Receives immediate feedback -> Reviews session performance.
    3.  **Progress Review:** User navigates to analytics dashboard -> Views historical performance for both modules.
- **UI/UX Considerations:**
    - Clean, focused, and intuitive interface.
    - Non-intrusive timer for mental math.
    - Clear display of questions, input areas, and feedback.
    - Simple navigation between modules and analytics.
</context>
<PRD>
# Technical Architecture
- **System Components:**
    - **Backend:** Python with FastAPI.
    - **Frontend:** Locally-served web interface using plain HTML, CSS, and TypeScript (no framework for MVP).
    - **Database:** SQLite (local, file-based).
    - **LLM Integration:** Python libraries (e.g., `openai`, `huggingface_hub`, `llm`) for chosen LLM API.
- **Data Models (SQLite):**
    - `questions`: `id` (PK), `topic` (TEXT), `difficulty` (TEXT), `content` (JSONB/TEXT), `solution` (TEXT).
    - `user_attempts`: `id` (PK), `question_id` (FK to `questions.id`), `user_answer` (TEXT), `is_correct` (BOOLEAN), `timestamp` (DATETIME), `time_taken_ms` (INTEGER).
    - (Potentially) `mental_math_sessions`: `id` (PK), `user_id` (if accounts added later), `score` (INTEGER), `duration_ms` (INTEGER), `timestamp` (DATETIME), `settings` (JSONB for difficulty/type).
- **APIs and Integrations:**
    - **Internal FastAPI Endpoints:**
        - `GET /api/question/random`: Fetches a random probability/statistics question.
        - `POST /api/submit-answer`: Submits probability/statistics answer, interacts with LLM, stores attempt.
        - `GET /api/mental-math/drill`: Generates a set of mental math problems.
        - `POST /api/mental-math/submit-session`: (If session-based) Submits mental math session results.
        - `GET /api/analytics/probability`: Retrieves probability module analytics.
        - `GET /api/analytics/mental-math`: Retrieves mental math module analytics.
    - **External LLM API:** Interaction via Python backend for Socratic tutoring. API keys managed via `.env`.
- **Infrastructure Requirements (MVP):**
    - Local machine capable of running Python, FastAPI, and a modern web browser.
    - Internet access for LLM API calls.

# Development Roadmap
- **MVP Requirements (Phased Approach):**
    - **Phase 1: Backend Core & Logic Engine**
        - Python virtual environment setup.
        - SQLite database schema design and initialization (`questions`, `user_attempts` tables).
        - Script for populating `questions` table (10-15 initial questions).
        - Core LLM Socratic Tutor function (Python) with robust prompt engineering and structured JSON response handling.
        - Simple CLI (`main_cli.py`) to test the backend loop (fetch question, get input, LLM process, display feedback).
    - **Phase 2: Building the Local Web User Interface**
        - FastAPI backend expansion: Endpoints for random question (`GET /api/question/random`), answer submission (`POST /api/submit-answer`).
        - FastAPI configured to serve static frontend assets (`index.html`, `style.css`, compiled `main.js`).
        - Frontend scaffolding: `index.html` (question display, answer textarea, submit button, feedback area), `style.css`.
        - Frontend logic (`main.ts`): Fetch random question on load, populate UI, handle answer submission via POST, display LLM feedback. TypeScript compilation setup.
    - **Phase 3: Implementing the Mental Math Module**
        - Backend: `math_generator.py` for dynamic problem generation. New FastAPI endpoint `GET /api/mental-math/drill`.
        - Frontend: New UI section/page for Mental Math. Countdown timer (`setInterval`/`clearInterval`). Interface for displaying problems and accepting inputs. Client-side answer checking for immediate feedback. Session score display.
    - **Phase 4: Persistence and Performance Analytics**
        - Backend: `POST /api/submit-answer` updated to save attempts to `user_attempts`. Similar logging for mental math scores.
        - Backend: New analytics endpoints `GET /api/analytics/probability` and `GET /api/analytics/mental-math`.
        - Frontend: "Progress" page to fetch and display analytics data (tables/simple stats).
- **Future Enhancements (Post-MVP):**
    - Full Web App Deployment (PaaS like Heroku/DigitalOcean App Platform).
    - Additional Modules: Market Microstructure, Brain Teasers, Programming (Python/SQL).
    - User Accounts.
    - Advanced UI/UX (React/Vue with component library).
    - Market Making Simulator.

# Logical Dependency Chain
1.  **Phase 1 (Backend Core):** Foundational. Everything depends on this.
    - Environment Setup
    - Database Schema & Init
    - Initial Content Population
    - LLM Tutor Implementation (core logic)
    - CLI for testing
2.  **Phase 2 (Local Web UI for Probability/Stats):** Builds on Phase 1 to make it usable.
    - FastAPI Endpoints for P/S module
    - HTML/CSS for P/S module
    - TypeScript logic for P/S module (API calls, DOM updates)
3.  **Phase 3 (Mental Math Module):** Can be developed somewhat in parallel to Phase 2's frontend, but depends on a running FastAPI backend.
    - Backend logic for math generation & endpoint
    - Frontend UI for mental math (new page/section, timer, problem display)
4.  **Phase 4 (Persistence & Analytics):** Depends on both modules being functional to have data to persist and analyze.
    - Database interaction updates for both modules
    - Analytics endpoints
    - Frontend dashboard for analytics

# Risks and Mitigations
- **Technical Challenges:**
    - **LLM Prompt Engineering:** Achieving reliable Socratic behavior and structured JSON output from the LLM can be iterative.
        - **Mitigation:** Start with the example system prompt structure. Allocate specific time for refining prompts based on test interactions. Implement robust error handling for LLM API responses (e.g., unexpected format, API errors).
    - **TypeScript Learning Curve:** If new to TypeScript, initial frontend development might be slower.
        - **Mitigation:** Focus on core TypeScript concepts for DOM manipulation and API calls first. Keep frontend simple for MVP. Leverage online resources and documentation.
- **Figuring out the MVP that we can build upon:**
    - **Scope Creep:** Adding too many features to the MVP.
        - **Mitigation:** Strictly adhere to the defined MVP features. Defer all other ideas to "Future Enhancements." The phased approach helps maintain focus.
    - **Over-engineering:** Making initial solutions too complex.
        - **Mitigation:** Prioritize functionality for the MVP. For example, plain HTML/CSS/TS for frontend is a deliberate choice to avoid framework overhead initially.
- **Resource Constraints:**
    - **Time:** As a solo project, time is the main constraint.
        - **Mitigation:** The phased development plan breaks the project into smaller, achievable chunks. Focus on one phase at a time.
    - **LLM API Costs:** Depending on the chosen LLM and usage, API calls can incur costs.
        - **Mitigation:** For development, use a free tier if available or a less expensive model. Implement caching for LLM responses to identical inputs if feasible (though less relevant for Socratic interaction which should be dynamic). Monitor API usage.

# Appendix
- **Research Findings:**
    - Key quant interview topics: Expected Value, Bayesian Reasoning, Combinatorics, Conditional Probability, Distributions, Mental Math speed.
    - Recommended resources: "Fifty Challenging Problems in Probability," QuantGuide, TraderMath.
    - LLM Socratic tutoring is a promising approach for guided learning.
- **Technical Specifications:**
    - Python version: (Specify, e.g., 3.9+)
    - FastAPI version: (Latest stable)
    - TypeScript version: (Latest stable)
    - LLM: (Specify chosen model, e.g., GPT-4, Gemini Pro)
    - System Prompt for LLM (initial version):
        ```
        <Role> You are an expert Socratic Tutor specializing in quantitative finance interview preparation. Your personality is encouraging but rigorous. You never give the direct answer upfront. </Role>
        <Context> The user is solving a probability/statistics problem. They have provided their thought process. Your task is to analyze their reasoning, identify the first logical error or point of confusion, and guide them to self-correction with a probing question. </Context>
        <Task> 1. Analyze the user's reasoning provided. 2. If it's correct, confirm it and provide a slightly more elegant or alternative solution. 3. If it's incorrect, DO NOT give the answer. Instead, ask a single, targeted question that helps the user identify their own mistake. 4. Respond ONLY in a structured JSON format: {"analysis": "Your reasoning is sound up to the point of calculating combinations. However...", "guidance_question": "Have you considered whether the events are independent or dependent at that stage?", "status": "needs_correction"} </Task>
        ```
</PRD>