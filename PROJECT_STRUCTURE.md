# QuantPrepper Project Structure

This document outlines the recommended project structure for the QuantPrepper application.

```bash
/QuantPrepper
|
├── app/                      # Main backend application package
│   ├── __init__.py
│   ├── main.py               # FastAPI app entrypoint, includes API routers
│   │
│   ├── api/                  # API endpoint definitions (routers)
│   │   ├── __init__.py
│   │   └── v1/
│   │       ├── __init__.py
│   │       ├── api_router.py   # Includes all endpoints for v1
│   │       └── endpoints/
│   │           ├── __init__.py
│   │           ├── probability.py  # Endpoints for probability module
│   │           └── mental_math.py  # Endpoints for mental math module
│   │
│   ├── core/                 # Core logic and configuration
│   │   ├── __init__.py
│   │   ├── config.py         # Settings management (e.g., reading .env)
│   │   └── llm_tutor.py      # Your LLM Socratic tutor logic (Task 24)
│   │
│   ├── db/                   # Database-related files
│   │   ├── __init__.py
│   │   ├── base.py           # SQLAlchemy engine, session, and Base (Subtask 22.1)
│   │   ├── models.py         # All SQLAlchemy models (Subtasks 22.2, 22.3)
│   │   └── init_db.py        # Script to initialize the database (Subtask 22.4)
│   │
│   ├── schemas/              # Pydantic schemas for API validation
│   │   ├── __init__.py
│   │   ├── question.py
│   │   └── attempt.py
│   │
│   └── services/             # Business logic
│       ├── __init__.py
│       └── math_generator.py # Mental math problem generator (Task 29)
│
├── static/                   # Frontend files served by FastAPI
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── main.js           # Compiled TypeScript output
│   └── index.html
│
├── ts/                       # TypeScript source files
│   └── main.ts
│
├── .env                      # For API keys and other secrets (DO NOT COMMIT)
├── .gitignore
├── main_cli.py               # Your CLI for testing the backend (Task 25)
├── package.json              # For npm dependencies (e.g., typescript)
├── tsconfig.json             # TypeScript compiler options
├── pyproject.toml            # Python project metadata and dependencies
├── uv.lock
└── README.md
```
