## **Project Overview: The Local Quant Interview Prep Tool**

### **1. Vision & Core Purpose**

This project is a locally-run desktop application designed to provide a focused, adaptive, and intelligent learning environment for preparing for quantitative finance interviews. Its primary purpose is twofold:

1. **Targeted Skill Enhancement:** To create a mastery-level tool for the user's self-identified areas of weakness: **Probability & Statistics** and **Mental Math**.
    
2. **Practical Programming Development:** To serve as a hands-on project for strengthening proficiency in **Python** (backend logic, data handling, API interaction) and **TypeScript** (modern frontend development).
    

The application's core innovation lies in its integration with a Large Language Model (LLM) to act as a **Socratic Tutor**, providing not just answers, but guided, step-by-step explanations and feedback on the user's reasoning process.1

### **2. Product Details & Key Features (MVP)**

The Minimum Viable Product will be a self-contained application that runs entirely on your local machine, requiring no external deployment.

#### **Module 1: Probability & Statistics Deep-Dive Engine**

This is the intellectual core of the application.

- **Content:** A curated database of interview-style questions covering key concepts identified as crucial in quant interviews.4
    
    - **Topics:** Expected Value, Bayesian Reasoning, Combinatorics, Conditional Probability, Distributions, and brain-teaser style probability puzzles.7
        
    - **Sources:** Questions will be inspired by well-regarded resources such as "Fifty Challenging Problems in Probability," QuantGuide, and TraderMath.
        
- **LLM-Powered Socratic Tutor:** This feature transforms the app from a simple question bank into a dynamic learning partner.
    
    - **Functionality:** When you attempt a problem, you can submit your final answer or, more importantly, your step-by-step thought process. The LLM will then:
        
        1. **Provide Hints:** On request, offer subtle guidance without giving away the solution.
            
        2. **Deliver Step-by-Step Solutions:** After an attempt, generate a full, reasoned solution, often using a "Chain-of-Thought" approach to make the logic transparent.
            
        3. **Analyze Your Reasoning:** This is the most valuable part. The LLM will assess your methodology, identify logical fallacies or calculation errors, and suggest alternative approaches, mirroring a real tutoring session.
            

#### **Module 2: High-Speed Mental Math Trainer**

This module is designed to build the speed and accuracy required for the initial screening tests at firms like Optiver.10

- **Content:** Drills will be dynamically generated to cover:
    
    - **Arithmetic:** Multi-digit multiplication/division, fractions, and percentages.
        
    - **Sequences:** Identifying patterns and predicting the next number.
        
    - **Estimation:** Quickly approximating answers to complex calculations.
        
- **Functionality:**
    
    - **Timed Sessions:** A prominent, non-intrusive timer will enforce speed and simulate interview pressure.12
        
    - **Customizable Difficulty:** You will be able to adjust the complexity of the problems.
        
    - **Performance Analytics:** The app will track your speed and accuracy for each problem type, allowing you to identify and work on specific weaknesses.
        

### **3. Technical Architecture & Stack (Local MVP)**

The technology stack is chosen to align with your existing skills, learning goals, and the local-first nature of the MVP.14

- **Backend & Core Logic (Python):**
    
    - **Framework:** **FastAPI**. While Flask is simpler initially, FastAPI is the superior choice here. Its modern, high-performance nature, automatic data validation with Pydantic, and built-in interactive API documentation make it ideal for this project and provide a better foundation for a potential future web app.
        
    - **LLM Integration:** Using Python libraries like `openai`, `huggingface_hub`, or the generic `llm` library to interact with your chosen LLM API (e.g., GPT-4, Gemini). API keys will be managed securely using a local `.env` file.
        
- **Frontend (TypeScript & Web Technologies):**
    
    - **Framework:** None for the MVP. We will build a **locally-served web interface** using plain **HTML, CSS, and TypeScript**. This directly targets your goal of learning TypeScript by focusing on DOM manipulation and API communication, without the added complexity of a large framework like React or Vue.js for this initial stage.17
        
- **Database (Local):**
    
    - **System:** **SQLite**. It's a serverless, self-contained, file-based database engine. It's perfect for a local application, requires zero configuration, and is natively supported by Python's standard library.
        

---

## **Detailed Development Plan**

This plan breaks the project into four manageable phases. Each phase results in a tangible, functional upgrade to the application.

### **Phase 1: The Backend Core & Logic Engine**

**Goal:** Build and validate the entire backend logic, including question handling and LLM interaction, using a simple command-line interface (CLI).

1. **Environment Setup:**
    
    - Create a project directory.
        
    - Set up a Python virtual environment.
        
    - Install initial dependencies: `fastapi`, `uvicorn`, `pydantic`, and your chosen LLM library (e.g., `openai`).
        
2. **Database Schema Design & Initialization:**
    
    - Design a simple SQLite schema. You'll need at least two tables:
        
        - `questions`: `id`, `topic` (e.g., 'probability', 'statistics'), `difficulty`, `content` (JSONB/TEXT to hold the question text and any options), `solution` (TEXT).
            
        - `user_attempts`: `id`, `question_id` (foreign key), `user_answer` (TEXT), `is_correct` (BOOLEAN), `timestamp`, `time_taken_ms`.
            
    - Write a Python script to create the SQLite database file and these tables.
        
3. **Content Population:**
    
    - Manually create 10-15 high-quality probability and statistics questions.
        
    - Store them in a simple format (like a JSON file) and write a script to populate your SQLite `questions` table.
        
4. **LLM Socratic Tutor Implementation:**
    
    - Write the core Python function that will interact with the LLM API.
        
    - **Prompt Engineering is Key:** Design a robust "system prompt" to define the LLM's role as a Socratic Tutor. This is a critical step.1
        
        - **Example System Prompt Structure:**
            
            ```
            <Role> You are an expert Socratic Tutor specializing in quantitative finance interview preparation. Your personality is encouraging but rigorous. You never give the direct answer upfront. </Role>
            <Context> The user is solving a probability/statistics problem. They have provided their thought process. Your task is to analyze their reasoning, identify the first logical error or point of confusion, and guide them to self-correction with a probing question. </Context>
            <Task> 1. Analyze the user's reasoning provided. 2. If it's correct, confirm it and provide a slightly more elegant or alternative solution. 3. If it's incorrect, DO NOT give the answer. Instead, ask a single, targeted question that helps the user identify their own mistake. 4. Respond ONLY in a structured JSON format: {"analysis": "Your reasoning is sound up to the point of calculating combinations. However...", "guidance_question": "Have you considered whether the events are independent or dependent at that stage?", "status": "needs_correction"} </Task>
            ```
            
    - Ensure your code can handle the structured JSON response from the LLM.20
        
5. **Build & Test with a CLI:**
    
    - Create a simple `main_cli.py` script.
        
    - This script will: fetch a random question from the database, print it to the console, accept a text input for the answer/reasoning, send it to your LLM function, and print the formatted JSON response. This validates the entire backend loop without needing a UI.
        

### **Phase 2: Building the Local Web User Interface**

**Goal:** Replace the CLI with a clean, functional web interface served locally by FastAPI.

1. **FastAPI Backend Expansion:**
    
    - In your main Python file (`main.py`), use FastAPI to create API endpoints 22:
        
        - `GET /api/question/random`: Fetches a random question from the database and returns it as JSON.
            
        - `POST /api/submit-answer`: Receives a question ID and the user's answer, processes it with the LLM Tutor logic from Phase 1, and returns the JSON feedback.
            
    - Configure FastAPI to serve a static `index.html` file and other frontend assets (CSS, JS).
        
2. **Frontend Scaffolding (HTML & CSS):**
    
    - Create an `index.html` file with the basic layout: a space for the question, a text area for the answer, a submit button, and a section to display feedback.
        
    - Create a `style.css` file to make the interface clean and readable.
        
3. **Frontend Logic (TypeScript):**
    
    - Set up your environment to compile TypeScript to JavaScript.
        
    - Write `main.ts` to handle all user interactions:
        
        - On page load, make a `fetch` call to your `/api/question/random` endpoint.
            
        - Use the response to populate the question area of the page.
            
        - Add an event listener to the submit button.
            
        - On submit, get the text from the answer area and make a `POST` request to `/api/submit-answer`.
            
        - Parse the JSON feedback from the response and display the `analysis` and `guidance_question` in the feedback area.
            

### **Phase 3: Implementing the Mental Math Module**

**Goal:** Add the second core module to the application for speed training.

1. **Backend Logic:**
    
    - Create a new Python file (`math_generator.py`) with functions to dynamically generate mental math problems (e.g., `generate_multiplication()`, `generate_sequence()`).
        
    - Add new FastAPI endpoints:
        
        - `GET /api/mental-math/drill`: Returns a set of, for example, 20 generated problems as JSON.
            
2. **Frontend Development:**
    
    - Create a new page/section in your UI for the Mental Math Trainer.
        
    - **Timer Implementation:** Write TypeScript code to implement a countdown timer. This will involve using `setInterval` to update the display every second and `clearInterval` to stop it.
        
    - Build the interface to display problems one by one and accept quick inputs.
        
    - Implement logic to check answers on the client-side (since it's simple math) for immediate feedback.
        
    - At the end of the timed session, display the score (e.g., "35 correct in 2 minutes").
        

### **Phase 4: Persistence and Performance Analytics**

**Goal:** Make the application stateful by saving user progress and providing performance insights.

1. **Database Interaction:**
    
    - Expand your backend `POST /api/submit-answer` endpoint to save the result of each attempt to the `user_attempts` table in your SQLite database.
        
    - Create a similar process for the mental math module to log session scores.
        
2. **Analytics Endpoints:**
    
    - Create new FastAPI endpoints to retrieve performance data:
        
        - `GET /api/analytics/probability`: Returns statistics like total attempted, accuracy percentage, and average time per question for the probability topic.
            
        - `GET /api/analytics/mental-math`: Returns historical scores and speed metrics.
            
3. **Frontend Dashboard:**
    
    - Create a "Progress" page in your UI.
        
    - Use TypeScript to fetch data from the analytics endpoints.
        
    - Display the data in a simple, clear format (e.g., tables or simple text statistics) to show your improvement over time.
        

### **Future Enhancements (Post-MVP)**

Once the local MVP is complete and serving you well, you can use it as a foundation to work on more advanced development tasks.

- **Full Web App Deployment:** Deploy the application online using a Platform-as-a-Service (PaaS) like Heroku or DigitalOcean App Platform, which are generally easier for solo developers than AWS Elastic Beanstalk. This would involve configuring the app for a production environment.
    
- **Add More Modules:** Expand the question bank to include **Market Microstructure**, **Brain Teasers**, and **Programming** (Python/SQL) challenges.
    
- **User Accounts:** Implement user authentication to create a multi-user system.
    
- **Advanced UI/UX:** Rebuild the frontend using a full framework like **React** or **Vue.js** with a component library like **MUI** or **Vuetify** for a more polished experience.
    
- **Market Making Simulator:** A major feature involving a more complex, game-like interactive interface.